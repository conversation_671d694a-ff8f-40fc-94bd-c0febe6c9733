#!/bin/bash

# Google Search Cycle Runner
# Author: ji<PERSON> <PERSON>i
# Create Time: 2025/8/2
# File Name: run_cycle.sh
# Description: Runs main.py in 30-minute cycles with 1-minute breaks and browser cleanup

# Configuration
MAIN_SCRIPT="/root/google-search/main.py"
CLEANUP_SCRIPT="/root/google-search/sh/cleanup_browsers.sh"
RUN_DURATION=1800  # 30 minutes in seconds (30 * 60)
BREAK_DURATION=60  # 1 minute in seconds
LOG_FILE="/root/google-search/logs/cycle.log"

# Create logs directory if it doesn't exist
mkdir -p "$(dirname "$LOG_FILE")"

# Function to log messages with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to cleanup and exit gracefully
cleanup_and_exit() {
    log_message "🛑 收到停止信号，正在清理..."
    
    # Kill main.py if it's running
    if [ ! -z "$MAIN_PID" ] && kill -0 "$MAIN_PID" 2>/dev/null; then
        log_message "⏹️ 停止主程序 (PID: $MAIN_PID)"
        kill -TERM "$MAIN_PID" 2>/dev/null
        sleep 3
        # Force kill if still running
        if kill -0 "$MAIN_PID" 2>/dev/null; then
            kill -KILL "$MAIN_PID" 2>/dev/null
        fi
    fi
    
    # Run browser cleanup
    log_message "🧹 清理浏览器进程..."
    bash "$CLEANUP_SCRIPT"
    
    log_message "✅ 清理完成，程序退出"
    exit 0
}

# Set up signal handlers
trap cleanup_and_exit SIGINT SIGTERM

# Main cycle loop
cycle_count=0

log_message "🚀 启动循环脚本"
log_message "📋 配置: 运行时间=${RUN_DURATION}秒(30分钟), 休息时间=${BREAK_DURATION}秒(1分钟)"
log_message "📁 主程序: $MAIN_SCRIPT"
log_message "🧹 清理脚本: $CLEANUP_SCRIPT"

while true; do
    cycle_count=$((cycle_count + 1))
    log_message "🔄 ===== 开始第 $cycle_count 个循环 ====="
    
    # Step 1: Browser cleanup
    log_message "🧹 步骤1: 清理浏览器缓存..."
    if [ -f "$CLEANUP_SCRIPT" ]; then
        bash "$CLEANUP_SCRIPT"
        log_message "✅ 浏览器清理完成"
    else
        log_message "⚠️ 清理脚本不存在: $CLEANUP_SCRIPT"
    fi
    
    # Step 2: Run main.py for 30 minutes
    log_message "🏃 步骤2: 启动主程序，运行30分钟..."
    
    if [ -f "$MAIN_SCRIPT" ]; then
        # Start main.py in background
        cd /root/google-search
        python3 "$MAIN_SCRIPT" &
        MAIN_PID=$!
        
        log_message "▶️ 主程序已启动 (PID: $MAIN_PID)"
        
        # Wait for 30 minutes or until process ends
        start_time=$(date +%s)
        while [ $(($(date +%s) - start_time)) -lt $RUN_DURATION ]; do
            # Check if process is still running
            if ! kill -0 "$MAIN_PID" 2>/dev/null; then
                log_message "⚠️ 主程序提前结束"
                break
            fi
            sleep 10  # Check every 10 seconds
        done
        
        # Stop main.py if still running
        if kill -0 "$MAIN_PID" 2>/dev/null; then
            log_message "⏰ 30分钟到达，停止主程序"
            kill -TERM "$MAIN_PID" 2>/dev/null
            sleep 5
            # Force kill if still running
            if kill -0 "$MAIN_PID" 2>/dev/null; then
                log_message "🔨 强制停止主程序"
                kill -KILL "$MAIN_PID" 2>/dev/null
            fi
        fi
        
        log_message "✅ 主程序运行周期完成"
    else
        log_message "❌ 主程序不存在: $MAIN_SCRIPT"
        log_message "💤 等待30分钟后继续..."
        sleep $RUN_DURATION
    fi
    
    # Step 3: Break for 1 minute
    log_message "😴 步骤3: 休息1分钟..."
    sleep $BREAK_DURATION
    log_message "⏰ 休息结束"
    
    log_message "🔄 ===== 第 $cycle_count 个循环完成 ====="
    echo ""  # Add empty line for readability
done
