# -*- coding: utf-8 -*-
"""
测试独立线程模式
"""
import logging
import sys
import os
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from spider import GoogleSearchThreadManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def test_independent_threads():
    """测试独立线程模式"""
    print("=== 测试独立线程模式 ===")
    
    # 创建线程管理器（使用较小的配置进行测试）
    manager = GoogleSearchThreadManager(
        num_threads=3,  # 只用3个线程测试
        batch_size=30,  # 总批次大小
        save_frequency=5  # 每5个查询保存一次
    )
    
    # 计算每个线程的批次大小
    batch_size_per_thread = 30 // 3  # 每个线程10个查询
    print(f"每个线程批次大小: {batch_size_per_thread}")
    
    # 启动独立线程处理
    started = manager.start_batch_processing(batch_size_per_thread)
    
    if started:
        print("✅ 独立线程模式启动成功")
        
        # 监控线程状态
        start_time = time.time()
        while True:
            alive_threads = [t for t in manager.threads if t.is_alive()]
            print(f"活跃线程数: {len(alive_threads)}/{len(manager.threads)}")
            
            if not alive_threads:
                print("所有线程已完成")
                break
                
            # 避免无限等待
            if time.time() - start_time > 300:  # 5分钟超时
                print("测试超时，强制退出")
                break
                
            time.sleep(10)  # 每10秒检查一次
        
        print(f"总共处理了 {manager.completed_queries} 个查询")
    else:
        print("没有需要处理的查询")

if __name__ == "__main__":
    try:
        test_independent_threads()
        print("\n测试完成！")
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
