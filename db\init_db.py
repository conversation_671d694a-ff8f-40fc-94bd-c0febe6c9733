# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/3/19 20:01
File Name: init_db.py
"""
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
import os
from dotenv import load_dotenv

from db.db_models import Base

load_dotenv()
mysql_host = os.getenv('MYSQL_HOST', '************')
mysql_port = os.getenv('MYSQL_PORT', '13306')
mysql_user = os.getenv('MYSQL_USER', 'root')
mysql_password = os.getenv('MYSQL_PASSWORD', 'qwer1234')
mysql_db = os.getenv('MYSQL_DATABASE', 'ebay')
temp_engine = create_engine(f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}")

# 检查数据库是否存在，如果不存在则创建
with temp_engine.connect() as conn:
    conn.execute(text(f"CREATE DATABASE IF NOT EXISTS {mysql_db}"))
DATABASE_URL = f"mysql+pymysql://{mysql_user}:{mysql_password}@{mysql_host}:{mysql_port}/{mysql_db}"
engine = create_engine(DATABASE_URL,
                       pool_recycle=1800,
                       pool_pre_ping=True,
                       pool_size=50,        # 增加连接池大小到50
                       max_overflow=50,     # 增加最大溢出到50
                       pool_timeout=120,    # 增加连接超时时间到120秒
                       )

# Base.metadata.drop_all(engine)
# print("Drop all tables")
Base.metadata.create_all(engine)

SessionLocal = sessionmaker(autocommit=False, autoflush=True, bind=engine, expire_on_commit=False)
