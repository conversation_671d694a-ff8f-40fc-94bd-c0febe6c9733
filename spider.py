# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/3/19 13:15
File Name: spider.py
"""
import random
import time
from datetime import datetime
import logging

import requests
import threading
import re
from lxml import etree
import os

from sqlalchemy import func

from cookies import GoogleCookie
from proxy import ProxyManager
from db.db_models import SearchQueries, SearchResults
from db.init_db import SessionLocal, engine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('google_search.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('GoogleSearch')


class GoogleSearch:
    def __init__(self, thread_id=0, cookie_file=None, proxy_manager=None):
        """
        Initialize the GoogleSearch class

        Args:
            thread_id: 线程ID,用于区分不同实例
            cookie_file: Cookie文件路径 (默认: 基于thread_id生成不同的文件名)
            proxy_manager: 代理管理器实例 (默认: 创建新的MyProxyManager)
        """
        # 如果没有提供cookie_file，则基于thread_id生成唯一文件名
        folder = 'cookies'
        if not os.path.exists(folder):
            os.makedirs(folder)
        if cookie_file is None:
            cookie_file = f"cookies/google_cookies_{thread_id}.json"
        self.thread_id = thread_id
        self.google_cookie = GoogleCookie(cookie_file=cookie_file)
        self.proxy_manager = proxy_manager

        # Cookie失败计数管理
        self.cookie_failure_count = 0
        self.max_cookie_failures = 5
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
        ]
        self.base_headers = {
            'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'downlink': '10',
            'priority': 'u=0, i',
            'rtt': '50',
            'sec-ch-prefers-color-scheme': 'dark',
            'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
            'sec-ch-ua-arch': '"x86"',
            'sec-ch-ua-bitness': '"64"',
            'sec-ch-ua-form-factors': '"Desktop"',
            'sec-ch-ua-full-version': '"133.0.6943.142"',
            'sec-ch-ua-full-version-list': '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.142", "Chromium";v="133.0.6943.142"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-model': '""',
            'sec-ch-ua-platform': '"Windows"',
            'sec-ch-ua-platform-version': '"15.0.0"',
            'sec-ch-ua-wow64': '?0',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'none',
            'sec-fetch-user': '?installer.txt',
            'upgrade-insecure-requests': 'installer.txt',
            'x-browser-channel': 'stable',
            'x-browser-copyright': 'Copyright 2025 Google LLC. All rights reserved.',
            'x-browser-year': '2025',
        }
        self.lock = threading.Lock()  # 添加线程锁用于同步操作

    def reset_cookie(self):
        """重置Cookie并重置失败计数器，确保获取到有效的cookie"""
        max_retries = 5  # 最大重试次数
        retry_delay = 2  # 重试间隔（秒）

        try:
            with self.lock:
                logger.info(f"[线程 {self.thread_id}] Cookie累计失败{self.cookie_failure_count}次，重新生成Cookie")

                # 重试获取有效cookie
                for attempt in range(max_retries):
                    try:
                        logger.info(f"[线程 {self.thread_id}] 尝试获取新Cookie (第{attempt + 1}/{max_retries}次)")
                        new_cookies = self.google_cookie.create_cookies()

                        # 检查获取到的cookie是否有效
                        if new_cookies and self.google_cookie.is_cookies_valid(new_cookies):
                            self.google_cookie.cookies = new_cookies
                            self.cookie_failure_count = 0
                            # 显示新Cookie的关键信息
                            cookie_info = self.get_cookie_info()
                            logger.info(f"[线程 {self.thread_id}] 新Cookie已生成: {cookie_info}")
                            return  # 成功获取有效cookie，退出方法
                        else:
                            logger.warning(f"[线程 {self.thread_id}] 第{attempt + 1}次获取的Cookie无效或为空: {new_cookies}")
                            if attempt < max_retries - 1:  # 不是最后一次尝试
                                logger.info(f"[线程 {self.thread_id}] 等待{retry_delay}秒后重试...")
                                time.sleep(retry_delay)

                    except Exception as retry_e:
                        logger.error(f"[线程 {self.thread_id}] 第{attempt + 1}次获取Cookie时出错: {retry_e}")
                        if attempt < max_retries - 1:  # 不是最后一次尝试
                            logger.info(f"[线程 {self.thread_id}] 等待{retry_delay}秒后重试...")
                            time.sleep(retry_delay)

                # 所有重试都失败
                logger.error(f"[线程 {self.thread_id}] 经过{max_retries}次尝试，仍无法获取有效Cookie")
                # 保持原有的cookie_failure_count，不重置，这样可能会触发更多重试

        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 重置Cookie失败: {e}")

    def get_cookie_info(self):
        """获取Cookie的关键信息用于日志"""
        try:
            cookies = self.google_cookie.cookies
            if cookies:
                # 获取关键Cookie信息
                key_cookies = []
                for name in ['HSID', 'SSID', 'APISID', 'SAPISID']:
                    if name in cookies:
                        value = cookies[name][:8] + "..." if len(cookies[name]) > 8 else cookies[name]
                        key_cookies.append(f"{name}={value}")
                return ", ".join(key_cookies) if key_cookies else "无关键Cookie"
            return "Cookie为空"
        except Exception as e:
            return f"获取Cookie信息失败: {e}"

    def increment_cookie_failure(self):
        """增加Cookie失败计数，如果达到阈值则重置Cookie"""
        self.cookie_failure_count += 1
        logger.warning(f"[线程 {self.thread_id}] Cookie失败计数: {self.cookie_failure_count}/{self.max_cookie_failures}")

        if self.cookie_failure_count >= self.max_cookie_failures:
            self.reset_cookie()
            return True  # 表示已重置Cookie
        return False  # 表示未重置Cookie

    def get_headers(self):
        """生成带有随机User-Agent的请求头"""
        headers = self.base_headers.copy()
        headers['user-agent'] = random.choice(self.user_agents)
        return headers

    def check_cookies_valid(self, page):
        h3_elements = page.xpath('//h3')
        return len(h3_elements) >= 1

    def get_email(self, content):
        """从内容中提取邮箱地址，增强版"""
        if not content:
            return None

        # 基本邮箱正则表达式 - 更严格的域名匹配
        basic_pattern = r'[\w\.-]+@[\w\.-]+\.(?:com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx|ch|se|no|dk|fi|pl|cz|at|be|ie|nz|sg|hu|pt|gr|il|za|tr|ro|hk|kr|tw|vn|id|th|my|ph|sa|ae|qa|pk|bd|ng|ke|tz|gh|et|dz|ma|tn|eg|ly|zw|zm|mw|mu|mg|ci|cm|sn|cd|ao|na|bw|ls|sz|rw|bi|tg|bj|ne|ml|mr|td|gm|sl|lr|gn|gw|cv|st|ga|cg|cf|sd|ss|er|dj|so)'

        # 尝试提取标准邮箱
        emails = re.findall(basic_pattern, content, re.IGNORECASE)
        if emails:
            return emails[0].strip()

        # 处理带空格的邮箱
        spaced_email_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx)'
        spaced_match = re.search(spaced_email_pattern, content, re.IGNORECASE)
        if spaced_match:
            return f"{spaced_match.group(1)}@{spaced_match.group(2)}.{spaced_match.group(3)}"

        # 针对特殊情况：邮箱后面紧跟着句点和文本
        # 例如: "hughnick1@ outlook.com. Village Website"
        special_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk)(?:\s*\.\s*|\s+)'
        special_match = re.search(special_pattern, content, re.IGNORECASE)
        if special_match:
            return f"{special_match.group(1)}@{special_match.group(2)}.{special_match.group(3)}"

        # 尝试查找并组合分散的邮箱部分
        if '@' in content:
            # 使用更复杂的正则表达式来处理分散的邮箱
            combined_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*([a-zA-Z]{2,6})'
            combined_match = re.search(combined_pattern, content)
            if combined_match:
                username = combined_match.group(1).strip()
                domain = combined_match.group(2).strip()
                tld = combined_match.group(3).strip()
                return f"{username}@{domain}.{tld}"

        return None

    def search_page(self, keyword, page_num=0, max_retries=2, proxy=None, error_count=0):
        """
        搜索特定页码的结果并提取URL和邮箱

        Args:
            keyword: 搜索关键词
            page_num: 页码 (0表示第一页)
            max_retries: 最大重试次数（减少重试，失败时快速跳过）
            proxy: 使用的代理
            error_count: 当前错误计数

        Returns:
            结果字典，包含results和has_next，以及更新后的proxy和error_count
        """
        # 搜索参数
        params = {
            'q': keyword,
            'oq': keyword,
            'start': page_num * 10,  # Google每页10个结果
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }

        # 设置代理
        if proxy:
            proxy_server = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
        else:
            proxy_server = None

        for attempt in range(max_retries):
            cookies = self.google_cookie.cookies

            # 发送请求，使用当前代理
            try:
                headers = self.get_headers()
                res = requests.get(
                    url='https://www.google.com/search',
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=10,
                    proxies=proxy_server
                )

                if res.status_code == 200:
                    page = etree.HTML(res.text)

                    # 检查是否有有效结果
                    if not self.check_cookies_valid(page):
                        logger.info(f"[线程 {self.thread_id}] 页面没有有效结果，Cookie可能失效")
                        self.increment_cookie_failure()
                        error_count += 1
                        continue

                    # 提取搜索结果
                    url_elements = page.xpath('//a[@jsname="UWckNb"]/@href')

                    # 获取URL和内容列表
                    urls = [url for url in url_elements]
                    contents = [
                        ' '.join(page.xpath(f'(//div[@jscontroller="SC7lYd"]//*[@data-snf="nke7rc"])[{i + 1}]//text()'))
                        for i in range(len(url_elements))]

                    # 确保内容列表长度与URL列表相同
                    while len(contents) < len(urls):
                        contents.append('')  # 如果内容少于URL，添加空内容

                    # 提取结果
                    results = []
                    for i, url in enumerate(urls):
                        try:
                            content = contents[i] if i < len(contents) else ''

                            # 提取邮箱
                            email = self.get_email(content)
                            if email:
                                results.append((url, email))
                        except Exception as e:
                            logger.error(f"[线程 {self.thread_id}] 提取结果 {i + 1} 时出错: {e}")

                    # 检查是否有下一页
                    has_next = page.xpath('//a[@id="pnnext"]')

                    # 成功获取结果，重置错误计数
                    return {
                        'results': results,
                        'has_next': len(has_next) > 0,
                        'proxy': proxy,
                        'error_count': 0  # 成功后重置错误计数
                    }

                elif res.status_code == 429:
                    logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}，Cookie可能被限制")
                    self.increment_cookie_failure()
                    error_count += 1

                else:
                    logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {res.status_code}")
                    self.increment_cookie_failure()
                    error_count += 1

            except Exception as e:
                logger.error(f'[线程 {self.thread_id}] 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')
                self.increment_cookie_failure()
                error_count += 1

            # 等待一段时间后重试
            # time.sleep(random.uniform(installer.txt, 2))

        logger.error(f"[线程 {self.thread_id}] 所有 {max_retries} 次尝试均失败")

        # 返回失败结果和更新的错误计数
        return {
            'results': [],
            'has_next': False,
            'proxy': proxy,
            'error_count': error_count
        }

    def search(self, keyword, max_pages=None, max_retries=2, max_errors_before_proxy_change=3):
        """
        搜索并获取所有页面的结果

        Args:
            keyword: 搜索关键词
            max_pages: 最大爬取页数，None表示爬取所有页面
            max_retries: 最大重试次数（减少重试，失败时快速跳过）
            max_errors_before_proxy_change: 更换代理前允许的最大错误次数

        Returns:
            所有页面的结果列表，每个元素是(page_num, url, email)元组
        """
        all_results = []
        page_num = 0
        has_next = True
        error_count = 0

        # 初始获取一个代理
        proxy = self.proxy_manager.get_random_proxy()
        if proxy:
            logger.info(f"[线程 {self.thread_id}] 初始使用代理: {proxy}")
        else:
            logger.warning(f"[线程 {self.thread_id}] 没有可用代理")

        while has_next and (max_pages is None or page_num < max_pages):
            # 使用当前代理搜索页面
            page_result = self.search_page(keyword, page_num, max_retries, proxy, error_count)

            # 更新代理和错误计数
            proxy = page_result['proxy']
            error_count = page_result['error_count']

            # 如果错误计数超过阈值，更换代理
            if error_count >= max_errors_before_proxy_change:
                logger.warning(f"[线程 {self.thread_id}] 连续遇到 {error_count} 次错误，更换代理")

                # 获取新代理
                proxy = self.proxy_manager.get_random_proxy()
                if proxy:
                    logger.info(f"[线程 {self.thread_id}] 更换为新代理: {proxy}")
                else:
                    logger.warning(f"[线程 {self.thread_id}] 没有可用的新代理")

                error_count = 0  # 重置错误计数

            if not page_result['results']:
                logger.error(f"[线程 {self.thread_id}] 获取第 {page_num + 1} 页失败，停止爬取")
                break

            # 添加当前页面的结果
            for url, email in page_result['results']:
                all_results.append((page_num + 1, url, email))

            has_next = page_result['has_next']
            # logger.info(f"[线程 {self.thread_id}] 已爬取第 {page_num + installer.txt} 页，共找到 {len(page_result['results'])} 个结果")

            page_num += 1

            # 页面间添加随机延迟
            # if has_next and (max_pages is None or page_num < max_pages):
            #     time.sleep(random.uniform(installer.txt, 3))

        logger.info(f"[线程 {self.thread_id}] 完成搜索 '{keyword}'，共爬取 {page_num} 页，找到 {len(all_results)} 个结果")

        return all_results


class GoogleSearchThreadManager:
    def __init__(self, num_threads=5, batch_size=100, browser_path=None, save_frequency=5):
        """
        初始化多线程搜索管理器

        Args:
            num_threads: 线程数
            batch_size: 每批处理的数据量
            browser_path: 浏览器路径
            save_frequency: 每个线程处理多少个查询后保存一次数据库
        """
        self.num_threads = num_threads
        self.batch_size = batch_size
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        self.browser_path = browser_path
        self.save_frequency = save_frequency

        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.progress_callback = None

    def set_progress_callback(self, callback):
        """设置进度回调函数"""
        self.progress_callback = callback

    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.progress_callback and self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                self.progress_callback(self.completed_queries, self.total_queries, progress)
            logger.info(
                f"[线程 {thread_id}] 已完成 {completed} 个查询，总进度: {self.completed_queries}/{self.total_queries}")

    def get_uncrawled_queries_batch(self, batch_size=10):
        """从数据库获取一小批未爬取的查询"""
        db = None
        try:
            db = SessionLocal()
            # 获取指定数量的未爬取查询，使用随机排序
            queries = db.query(SearchQueries).filter(
                (SearchQueries.crawled_status.is_(None)) |
                (SearchQueries.crawled_status != 'completed')
            ).order_by(func.random()).limit(batch_size).all()

            data = []
            for query in queries:
                if query:
                    data.append((query.id, query.county, query.goods, query.email_type))

            logger.info(f"获取到 {len(data)} 条未处理查询")
            return data
        except Exception as e:
            logger.error(f"获取未爬取查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []
        finally:
            if db:
                db.close()

    def get_next_batch_and_distribute(self, batch_size=10):
        """获取下一批查询并分配给线程（每个线程1条数据）"""
        try:
            # 获取一小批未处理的查询
            batch_queries = self.get_uncrawled_queries_batch(batch_size)

            if not batch_queries:
                logger.info("没有更多查询需要处理")
                return []

            self.total_queries = len(batch_queries)
            logger.info(f"本批次获取到 {self.total_queries} 个查询")

            # 为每个查询分配一个线程（一对一分配）
            self.thread_queries = []
            for i, query_data in enumerate(batch_queries):
                if i < self.num_threads:  # 确保不超过线程数
                    self.thread_queries.append([query_data])  # 每个线程只处理一条查询
                else:
                    # 如果查询数超过线程数，将多余的查询分配给现有线程
                    thread_idx = i % self.num_threads
                    self.thread_queries[thread_idx].append(query_data)

            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                if queries:  # 只显示有查询的线程
                    logger.info(f"线程 {i} 分配了 {len(queries)} 个查询")

            return self.thread_queries
        except Exception as e:
            logger.error(f"获取和分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def worker(self, thread_id, batch_size_per_thread=1000):
        """
        工作线程函数 - 每个线程独立循环获取批次

        Args:
            thread_id: 线程ID
            batch_size_per_thread: 每个线程每次获取的查询数量（比如1000条）
        """
        # 为每个线程创建一个GoogleSearch实例，使用唯一的cookie文件
        searcher = GoogleSearch(thread_id=thread_id, proxy_manager=ProxyManager(num=50))

        # 显示当前Cookie信息
        cookie_info = searcher.get_cookie_info()
        logger.info(f"[线程 {thread_id}] 启动，当前Cookie: {cookie_info}")

        # 监控连接池状态
        pool = engine.pool
        logger.info(f"[线程 {thread_id}] 连接池状态: 大小={pool.size()}, 已检出={pool.checkedout()}, 溢出={pool.overflow()}")

        total_processed = 0
        local_results = {}  # 本地存储结果，减少数据库访问 {id: (search_results, success)}

        # 线程主循环：不断获取新批次处理
        while True:
            try:
                # 获取当前线程的批次，添加重试机制
                queries = None
                for retry in range(3):  # 最多重试3次
                    try:
                        queries = self.get_uncrawled_queries_batch(batch_size_per_thread)
                        break
                    except Exception as e:
                        logger.warning(f"[线程 {thread_id}] 获取查询批次失败 (第{retry+1}/3次): {e}")
                        if retry < 2:  # 不是最后一次重试
                            time.sleep(2)  # 等待2秒后重试

                if not queries:
                    logger.info(f"[线程 {thread_id}] 没有更多查询，线程退出")
                    break

                logger.info(f"[线程 {thread_id}] 获取新批次: {len(queries)} 个查询")

                # 处理当前批次的查询
                for query_id, county, goods, email_type in queries:
                    try:
                        # 构建查询字符串: county + goods + email_type
                        if email_type == '@email':
                            search_query = f'{county} {goods} {email_type}'
                        else:
                            search_query = f'{county} {goods} "{email_type}"'

                        logger.info(f"[线程 {thread_id}] 开始查询: {search_query}")

                        # 执行搜索，获取所有页面的结果
                        search_results = searcher.search(search_query)

                        # 检查搜索结果
                        if search_results:
                            # 有结果，标记为成功
                            local_results[query_id] = (search_results, True)

                            # 统计页数和邮箱数量
                            emails = []
                            pages = set()
                            for page_num, _, email in search_results:
                                if email:
                                    emails.append(email)
                                pages.add(page_num)

                            unique_emails = len(set(emails))
                            page_count = len(pages)
                            logger.info(f"[线程 {thread_id}] 查询成功: {search_query}, 找到{unique_emails}个邮箱，{page_count}页")
                        else:
                            # 无结果，可能是查询失败，跳过此查询，不标记为completed
                            logger.warning(f"[线程 {thread_id}] 查询无结果，跳过: {search_query}")
                            # 不添加到local_results，这样就不会更新数据库状态
                            continue

                    except Exception as e:
                        logger.error(f"[线程 {thread_id}] 处理查询 {county} {goods} {email_type} 时出错: {e}")
                        # 查询异常，跳过此查询，不标记为completed
                        logger.warning(f"[线程 {thread_id}] 查询异常，跳过: {search_query}")
                        continue

                    total_processed += 1

                    # 根据配置的频率保存数据库（减少数据库访问频率）
                    if len(local_results) >= self.save_frequency:
                        total_data_count = sum(len(results[0]) for results in local_results.values())
                        logger.info(f"[线程 {thread_id}] 保存到数据库: {len(local_results)}个查询, {total_data_count}条数据")
                        self.update_results_db(local_results, thread_id, county, goods, email_type)
                        local_results = {}  # 清空本地结果

                    # 添加随机延迟，避免请求过于频繁
                    # time.sleep(random.uniform(0.5, 1.5))

                logger.info(f"[线程 {thread_id}] 完成当前批次 {len(queries)} 个查询，继续获取下一批次...")

            except Exception as e:
                logger.error(f"[线程 {thread_id}] 批次处理出错: {e}")
                import traceback
                traceback.print_exc()
                # 出错后等待一段时间再继续
                time.sleep(5)

        # 处理剩余的结果
        if local_results:
            total_data_count = sum(len(results[0]) for results in local_results.values())
            logger.info(f"[线程 {thread_id}] 保存剩余结果: {len(local_results)}个查询, {total_data_count}条数据")
            self.update_results_db(local_results, thread_id, county, goods, email_type)
            self.update_progress(thread_id, len(local_results))

        logger.info(f"[线程 {thread_id}] 线程结束，总共处理了 {total_processed} 个查询")

    def update_results_db(self, results_dict, thread_id, city, goods, email_type):
        """
        更新数据库中的查询结果

        Args:
            results_dict: 查询结果字典 {query_id: (search_results, success)}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        db = None
        try:
            # 使用线程锁保护数据库操作
            with self.result_lock:
                # 添加数据库连接重试机制
                for retry in range(3):  # 最多重试3次
                    try:
                        db = SessionLocal()

                        # 更新结果
                        for query_id, (search_results, success) in results_dict.items():
                            query = db.query(SearchQueries).filter(SearchQueries.id == query_id).first()
                            if query:
                                # 如果查询到的邮箱和翻页数都是0，不更新状态（保持原状态）
                                if not search_results:  # search_results 为空列表表示邮箱数量为0且翻页数为0
                                    logger.info(f"[线程 {thread_id}] 查询 {query_id} 结果为空（邮箱数量和翻页数都是0），不更新crawled_status")
                                    # 跳过状态更新，保持原有状态
                                else:
                                    # 只有当有结果时才标记为completed
                                    query.crawled_status = 'completed'

                                # 如果搜索成功且有结果，保存结果到数据库
                                if success and search_results:
                                    for page_num, url, email in search_results:
                                        if email:
                                            result = SearchResults(
                                                url=url,
                                                email=email,
                                                current_page=page_num,
                                                city=city,
                                                goods=goods,
                                                email_type=email_type,
                                                date=datetime.now(),
                                            )
                                            db.add(result)

                        db.commit()
                        break  # 成功则跳出重试循环

                    except Exception as e:
                        if db:
                            db.rollback()
                        logger.error(f"[线程 {thread_id}] 更新数据库失败 (第{retry+1}/3次): {e}")
                        if retry < 2:  # 不是最后一次重试
                            time.sleep(2)  # 等待2秒后重试
                        else:
                            import traceback
                            traceback.print_exc()
                    finally:
                        if db:
                            db.close()
                            db = None

            logger.info(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到数据库")
        except Exception as e:
            logger.error(f"[线程 {thread_id}] 更新结果数据库失败: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if db:
                db.close()

    def start_batch_processing(self, batch_size_per_thread=1000):
        """启动独立线程模式：每个线程独立获取和处理批次"""
        # 检查是否有待处理的查询
        test_queries = self.get_uncrawled_queries_batch(1)
        if not test_queries:
            logger.info("没有需要处理的新查询")
            return False

        # 清空之前的线程列表
        self.threads = []

        # 创建并启动独立工作线程
        for i in range(self.num_threads):
            thread = threading.Thread(target=self.worker, args=(i, batch_size_per_thread))
            thread.daemon = True  # 设置为守护线程
            self.threads.append(thread)
            thread.start()
            logger.info(f"线程 {i} 已启动，每次处理 {batch_size_per_thread} 个查询")

        logger.info(f"已启动 {len(self.threads)} 个独立工作线程")
        return True

    def start(self):
        """兼容性方法：使用默认批处理大小启动"""
        return self.start_batch_processing(self.batch_size)

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()
        logger.info("所有线程已完成!")
