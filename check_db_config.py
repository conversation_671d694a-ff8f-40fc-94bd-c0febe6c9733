# -*- coding: utf-8 -*-
"""
检查数据库配置和连接状态
"""
import sys
import os
from sqlalchemy import create_engine, text
import time

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.init_db import engine, DATABASE_URL

def check_mysql_config():
    """检查MySQL服务器配置"""
    print("=== 检查MySQL服务器配置 ===")
    
    try:
        with engine.connect() as conn:
            # 检查最大连接数
            result = conn.execute(text("SHOW VARIABLES LIKE 'max_connections'"))
            max_connections = result.fetchone()
            print(f"MySQL最大连接数: {max_connections[1]}")
            
            # 检查当前连接数
            result = conn.execute(text("SHOW STATUS LIKE 'Threads_connected'"))
            current_connections = result.fetchone()
            print(f"当前连接数: {current_connections[1]}")
            
            # 检查最大使用过的连接数
            result = conn.execute(text("SHOW STATUS LIKE 'Max_used_connections'"))
            max_used = result.fetchone()
            print(f"历史最大连接数: {max_used[1]}")
            
            # 计算连接使用率
            usage_rate = (int(current_connections[1]) / int(max_connections[1])) * 100
            print(f"连接使用率: {usage_rate:.2f}%")
            
            return int(max_connections[1])
            
    except Exception as e:
        print(f"检查MySQL配置失败: {e}")
        return None

def check_connection_pool():
    """检查SQLAlchemy连接池配置"""
    print("\n=== 检查SQLAlchemy连接池配置 ===")
    
    pool = engine.pool
    print(f"连接池大小: {pool.size()}")
    print(f"最大溢出: {engine.pool._max_overflow}")
    print(f"总连接数限制: {pool.size() + engine.pool._max_overflow}")
    print(f"当前已检出连接: {pool.checkedout()}")
    print(f"当前溢出连接: {pool.overflow()}")
    
    return pool.size() + engine.pool._max_overflow

def test_concurrent_connections(num_connections=50):
    """测试并发连接"""
    print(f"\n=== 测试{num_connections}个并发连接 ===")
    
    connections = []
    try:
        start_time = time.time()
        
        for i in range(num_connections):
            try:
                conn = engine.connect()
                connections.append(conn)
                print(f"成功创建连接 {i+1}/{num_connections}")
            except Exception as e:
                print(f"创建连接 {i+1} 失败: {e}")
                break
        
        end_time = time.time()
        print(f"成功创建 {len(connections)} 个连接，耗时 {end_time - start_time:.2f} 秒")
        
        # 测试查询
        if connections:
            result = connections[0].execute(text("SELECT 1"))
            print(f"测试查询成功: {result.fetchone()}")
        
    finally:
        # 关闭所有连接
        for conn in connections:
            try:
                conn.close()
            except:
                pass
        print(f"已关闭所有测试连接")

def calculate_requirements():
    """计算连接需求"""
    print("\n=== 计算连接需求 ===")
    
    servers = 6
    threads_per_server = 40
    operations_per_thread = 2  # 获取查询 + 保存结果
    
    total_threads = servers * threads_per_server
    max_connections_needed = total_threads * operations_per_thread
    
    print(f"服务器数量: {servers}")
    print(f"每服务器线程数: {threads_per_server}")
    print(f"总线程数: {total_threads}")
    print(f"每线程操作数: {operations_per_thread}")
    print(f"理论最大连接需求: {max_connections_needed}")
    print(f"建议连接池大小: {max_connections_needed + 50} (留50个缓冲)")
    
    return max_connections_needed

def main():
    print("数据库连接配置检查工具")
    print("=" * 50)
    
    # 检查MySQL配置
    mysql_max = check_mysql_config()
    
    # 检查连接池配置
    pool_max = check_connection_pool()
    
    # 计算需求
    required = calculate_requirements()
    
    # 给出建议
    print("\n=== 配置建议 ===")
    if mysql_max and mysql_max < required:
        print(f"⚠️  MySQL最大连接数({mysql_max})不足，建议增加到{required + 100}")
        print("   可以在MySQL中执行: SET GLOBAL max_connections = 1000;")
    
    if pool_max < required:
        print(f"⚠️  连接池大小({pool_max})不足，建议增加到{required + 50}")
    
    if mysql_max and pool_max and mysql_max >= required and pool_max >= required:
        print("✅ 配置看起来足够支持当前需求")
    
    # 测试连接
    test_concurrent_connections(min(100, pool_max))

if __name__ == "__main__":
    main()
