# -*- coding: utf-8 -*-
"""
Optimized Batch Google Search Main Program

Author: ji<PERSON> wei
Create Time: 2025/8/2
File Name: main.py
"""
from spider import GoogleSearchThreadManager
import time
import logging
import os
if not os.path.exists('logs'):
    os.mkdir('logs')

# Configure logging - simplified format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('OptimizedBatchMain')

# Configuration
CONFIG = {
    'num_threads': 40,          # Number of threads
    'batch_size': 200,           # Number of queries to fetch from database per batch (NOT per thread)
    'batch_interval': 2,        # Interval between batches (seconds)
    'save_frequency': 5,        # Save to database every N processed queries per thread
}

# Configuration explanation:
# - num_threads: Total number of independent worker threads (40个独立线程)
# - batch_size: Number of queries each thread fetches per batch (每个线程每次获取的查询数量)
#   Example: batch_size=1000 means each thread gets 1000 queries per batch
#   When a thread finishes its 1000 queries, it fetches another 1000 queries
# - save_frequency: How often to save results to database per thread (每个线程每N条成功查询保存一次)
#   Example: save_frequency=10 means save every 10 successful queries per thread


def main():
    """Optimized batch processing main function"""
    
    logger.info(f"启动爬虫: {CONFIG['num_threads']} 个线程, 批处理大小 {CONFIG['batch_size']}")

    try:
        total_processed = 0

        # Create thread manager once
        manager = GoogleSearchThreadManager(
            num_threads=CONFIG['num_threads'],
            batch_size=CONFIG['batch_size'],
            save_frequency=CONFIG['save_frequency']
        )

        # Start independent thread processing
        # 每个线程获取的批次大小就是CONFIG['batch_size']
        batch_size_per_thread = CONFIG['batch_size']

        logger.info(f"每个线程批次大小: {batch_size_per_thread} 条查询")

        started = manager.start_batch_processing(batch_size_per_thread)

        if started:
            logger.info("✅ 独立线程模式启动成功，线程将自动获取和处理批次")
            # Wait for all threads to complete
            manager.wait_completion()

            # Count final results
            total_processed = manager.completed_queries
            logger.info(f"✅ 所有线程完成，总共处理了{total_processed}个查询")
        else:
            logger.info("🔚 没有需要处理的查询")

        # Final statistics
        logger.info(f"🎉 ===== 独立线程处理完成 =====")
        logger.info(f"📊 总计处理了{total_processed}个查询")
        logger.info(f"🎉 ========================")

    except KeyboardInterrupt:
        logger.info(f"⏹️ 用户停止: 已处理{total_processed if 'total_processed' in locals() else 0}个查询")
    except Exception as e:
        logger.error(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
