# -*- coding: utf-8 -*-
"""
Optimized Batch Google Search Main Program

Author: jian wei
Create Time: 2025/8/2
File Name: main.py
"""
from spider import GoogleSearchThreadManager
import time
import logging
import os
if not os.path.exists('logs'):
    os.mkdir('logs')

# Configure logging - simplified format
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('OptimizedBatchMain')

# Configuration
CONFIG = {
    'num_threads': 40,          # Number of threads
    'batch_size': 10000,           # Number of queries to fetch from database per batch (NOT per thread)
    'batch_interval': 2,        # Interval between batches (seconds)
    'save_frequency': 1000,        # Save to database every N processed queries per thread
}
# logs不存在就创建

# Configuration explanation:
# - num_threads: Total number of worker threads
# - batch_size: Number of queries fetched from database in one batch
#   Example: batch_size=10 means fetch 10 queries from DB, then distribute to 10 threads
#   If batch_size=10 and num_threads=10, each thread gets ~1 query
#   If batch_size=100 and num_threads=10, each thread gets ~10 queries
# - batch_interval: Wait time between processing batches
# - save_frequency: How often to save results to database per thread
#   Example: save_frequency=5 means save every 5 processed queries per thread


def main():
    """Optimized batch processing main function"""
    
    logger.info(f"启动爬虫: {CONFIG['num_threads']} 个线程, 批处理大小 {CONFIG['batch_size']}")

    try:
        total_processed = 0
        batch_count = 0
        successful_batches = 0
        empty_batches = 0
        max_empty_batches = 3  # Stop after 3 consecutive empty batches

        # Batch processing loop
        while True:
            batch_count += 1
            # Create thread manager
            manager = GoogleSearchThreadManager(
                num_threads=CONFIG['num_threads'],
                batch_size=CONFIG['batch_size'],
                save_frequency=CONFIG['save_frequency']
            )

            # Start batch processing
            batch_start_time = time.time()
            started = manager.start_batch_processing(CONFIG['batch_size'])

            if started:
                # Wait for current batch to complete
                manager.wait_completion()

                batch_end_time = time.time()
                batch_duration = batch_end_time - batch_start_time

                # Count batch results
                batch_processed = manager.completed_queries
                total_processed += batch_processed

                if batch_processed > 0:
                    successful_batches += 1
                    empty_batches = 0  # Reset empty batch counter
                    logger.info(f"✅ 第{batch_count}批完成: 处理了{batch_processed}个查询, 耗时{batch_duration:.1f}秒")
                else:
                    empty_batches += 1
                    logger.warning(f"⚠️ 第{batch_count}批: 无结果")

                # Batch interval
                time.sleep(CONFIG['batch_interval'])
                    
            else:
                empty_batches += 1

                # Stop if consecutive empty batches
                if empty_batches >= max_empty_batches:
                    logger.info(f"🔚 没有更多查询，总共处理了{total_processed}个")
                    break

                # Wait and retry
                time.sleep(CONFIG['batch_interval'] * 2)

        # Final statistics
        logger.info(f"🎉 ===== 批处理完成 =====")
        logger.info(f"📊 总计: {successful_batches}个成功批次，处理了{total_processed}个查询")
        if total_processed > 0:
            avg_per_batch = total_processed / successful_batches if successful_batches > 0 else 0
            logger.info(f"📈 平均每批: {avg_per_batch:.1f}个查询")
        logger.info(f"🎉 ========================")

    except KeyboardInterrupt:
        logger.info(f"⏹️ 用户停止: 已处理{total_processed}个查询")
    except Exception as e:
        logger.error(f"❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
