# -*- coding: utf-8 -*-
"""
验证MySQL最大连接数配置
"""
import sys
import os
from sqlalchemy import create_engine, text
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.init_db import engine

def verify_mysql_connections():
    """验证MySQL连接配置"""
    print("=" * 60)
    print("MySQL连接配置验证")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        with engine.connect() as conn:
            # 检查最大连接数
            result = conn.execute(text("SHOW VARIABLES LIKE 'max_connections'"))
            max_connections = result.fetchone()
            print(f"✅ 最大连接数: {max_connections[1]}")
            
            # 检查当前连接数
            result = conn.execute(text("SHOW STATUS LIKE 'Threads_connected'"))
            current_connections = result.fetchone()
            print(f"📊 当前连接数: {current_connections[1]}")
            
            # 检查最大使用过的连接数
            result = conn.execute(text("SHOW STATUS LIKE 'Max_used_connections'"))
            max_used = result.fetchone()
            print(f"📈 历史最大连接数: {max_used[1]}")
            
            # 检查其他相关配置
            configs_to_check = [
                'max_user_connections',
                'wait_timeout', 
                'interactive_timeout',
                'max_connect_errors'
            ]
            
            print("\n其他相关配置:")
            for config in configs_to_check:
                try:
                    result = conn.execute(text(f"SHOW VARIABLES LIKE '{config}'"))
                    row = result.fetchone()
                    if row:
                        print(f"  {config}: {row[1]}")
                except:
                    print(f"  {config}: 无法获取")
            
            # 计算使用率和建议
            max_conn = int(max_connections[1])
            current_conn = int(current_connections[1])
            max_used_conn = int(max_used[1])
            
            usage_rate = (current_conn / max_conn) * 100
            max_usage_rate = (max_used_conn / max_conn) * 100
            
            print(f"\n📊 连接使用情况:")
            print(f"  当前使用率: {usage_rate:.2f}%")
            print(f"  历史最高使用率: {max_usage_rate:.2f}%")
            
            # 给出建议
            print(f"\n💡 建议:")
            required_connections = 6 * 40 * 2  # 6台服务器 * 40线程 * 2操作
            
            if max_conn >= required_connections + 100:
                print(f"✅ 连接数配置充足 (需要{required_connections}，配置{max_conn})")
            elif max_conn >= required_connections:
                print(f"⚠️  连接数刚好够用，建议增加缓冲 (需要{required_connections}，配置{max_conn})")
            else:
                print(f"❌ 连接数不足！需要至少{required_connections}，当前只有{max_conn}")
                print(f"   建议设置为: {required_connections + 200}")
            
            return max_conn >= required_connections
            
    except Exception as e:
        print(f"❌ 连接MySQL失败: {e}")
        return False

def show_config_instructions():
    """显示配置说明"""
    print("\n" + "=" * 60)
    print("MySQL配置修改说明")
    print("=" * 60)
    
    print("""
1. 找到MySQL配置文件:
   常见位置: /etc/mysql/my.cnf 或 /etc/my.cnf
   
2. 编辑配置文件:
   sudo nano /etc/mysql/my.cnf
   
3. 在 [mysqld] 部分添加:
   [mysqld]
   max_connections = 1000
   max_user_connections = 950
   max_connect_errors = 100000
   wait_timeout = 28800
   interactive_timeout = 28800
   
4. 重启MySQL服务:
   sudo systemctl restart mysql
   
5. 验证配置:
   python verify_mysql_config.py
""")

def test_connection_creation():
    """测试连接创建"""
    print("\n" + "=" * 60)
    print("测试连接创建")
    print("=" * 60)
    
    connections = []
    max_test = 50  # 测试创建50个连接
    
    try:
        print(f"尝试创建 {max_test} 个连接...")
        
        for i in range(max_test):
            try:
                conn = engine.connect()
                connections.append(conn)
                if (i + 1) % 10 == 0:
                    print(f"  已创建 {i + 1} 个连接")
            except Exception as e:
                print(f"  第 {i + 1} 个连接创建失败: {e}")
                break
        
        print(f"✅ 成功创建 {len(connections)} 个连接")
        
        # 测试查询
        if connections:
            result = connections[0].execute(text("SELECT 'Connection test successful' as message"))
            message = result.fetchone()
            print(f"✅ 查询测试: {message[0]}")
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
    finally:
        # 清理连接
        for conn in connections:
            try:
                conn.close()
            except:
                pass
        print(f"🧹 已清理所有测试连接")

def main():
    print("MySQL连接配置验证工具")
    
    # 验证当前配置
    is_sufficient = verify_mysql_connections()
    
    # 如果配置不足，显示配置说明
    if not is_sufficient:
        show_config_instructions()
    
    # 测试连接创建
    test_connection_creation()
    
    print(f"\n{'='*60}")
    print("验证完成")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
